# AWOS Sound Player Container

A containerized AWOS (Automated Weather Observing System) audio player and streaming system for Ridge Landing Airpark. This system provides click-triggered weather reports, audio recording, and live HLS streaming capabilities.

## Overview

The sound-player-container is designed to:
- Detect microphone click patterns to trigger AWOS weather reports
- Fetch weather audio from the weather-api-container
- Play weather reports through speakers with relay control
- Record audio segments and upload to S3 storage
- Stream live audio via HLS to VPS for web access
- Provide GPIO control for radio transmission relay

## Architecture

### Core Components

- **AudioEventManager**: Central audio processing hub that manages real-time audio input
- **UnifiedSignalDetector**: Detects audio signals and click patterns
- **AWOSController**: Handles click sequence logic and triggers weather reports
- **RecordingController**: Manages audio recording and S3 uploads
- **HLSAudioSubscriber**: Converts audio to HLS segments for streaming
- **HLSVPSUploader**: Uploads HLS files to VPS via WebSocket
- **GpioController**: Controls radio transmission relay

### Audio Processing Flow

```
Microphone Input → AudioEventManager → UnifiedSignalDetector
                                    ↓
                              AWOSController (click detection)
                                    ↓
                              Weather API Request
                                    ↓
                              Audio Playback + Recording + HLS Streaming
```

## Configuration

All configuration is handled through environment variables. Key settings:

### Core System
- `VOLUME`: Audio volume 0-100 (default: 80)
- `API_BASE_URL`: Weather API endpoint (default: https://awosnew.skytraces.com)
- `HLS_STATION_ID`: Station ID for HLS uploads (default: 4FL5)

### Audio Processing
- `SAMPLE_RATE`: Audio sample rate in Hz (default: 48000)
- `CHANNELS`: Number of audio channels (default: 1)
- `CHUNK_SIZE`: Audio chunk size in samples (default: 1024)
- `FREQ_MIN_HZ`: Minimum frequency for analysis (default: 200)
- `FREQ_MAX_HZ`: Maximum frequency for analysis (default: 3500)

### Signal Detection
- `SIGNAL_THRESHOLD_HIGH`: High threshold for signal start (default: 50000)
- `SIGNAL_THRESHOLD_LOW`: Low threshold for signal end (default: 10000)
- `CLICK_MIN_DURATION`: Minimum click duration in seconds (default: 0.1)
- `CLICK_MAX_DURATION`: Maximum click duration in seconds (default: 0.6)
- `CLICK_COOLDOWN`: Timeout for click sequences in seconds (default: 1.0)
- `AWOS_CLICK_COUNT`: Number of clicks for AWOS (default: 3)
- `RADIO_CHECK_CLICK_COUNT`: Number of clicks for radio check (default: 4)

### Recording
- `ENABLE_RECORDING`: Enable audio recording (default: true)
- `RECORDING_STORAGE_PATH`: Directory for recordings (default: /app/recordings)
- `PRE_ROLL_SECONDS`: Pre-roll recording time (default: 0.5)
- `POST_ROLL_SECONDS`: Post-roll recording time (default: 1.0)
- `MIN_SEGMENT_DURATION`: Minimum recording duration (default: 0.1)
- `MAX_SEGMENT_DURATION`: Maximum recording duration (default: 30.0)

### S3 Cloud Storage
- `ENABLE_S3_UPLOAD`: Enable S3 uploads (default: false)
- `S3_ACCESS_KEY_ID`: S3 access key ID
- `S3_SECRET_ACCESS_KEY`: S3 secret access key
- `S3_ENDPOINT_URL`: S3 endpoint URL (for Cloudflare R2)
- `S3_BUCKET_NAME`: S3 bucket name (default: recordings)
- `STATION_NAME`: Station name for S3 folder structure

### HLS Streaming
- `ENABLE_HLS_STREAMING`: Enable HLS streaming (default: false)
- `HLS_SEGMENT_DURATION`: HLS segment duration in seconds (default: 2.0)
- `HLS_PLAYLIST_SIZE`: Number of segments in playlist (default: 6)
- `HLS_OUTPUT_PATH`: Directory for HLS files (default: /app/hls)
- `HLS_AUDIO_BITRATE`: Audio bitrate in kbps (default: 128)
- `HLS_VPS_UPLOAD_URL`: VPS endpoint for HLS uploads

## Usage

### Basic Operation

1. **Start the container**: The system automatically begins monitoring for audio input
2. **Click detection**: Make 3 quick clicks into the microphone (0.1-0.6 seconds each)
3. **Weather preparation**: After the 3rd click, weather data is prepared in background
4. **AWOS playback**: Weather report plays through speakers and is recorded
5. **Automatic upload**: Recordings are uploaded to S3 and HLS streams to VPS

### Click Patterns

- **3 clicks**: Triggers AWOS weather report
- **4 clicks**: Triggers radio check (placeholder functionality)

### Docker Deployment

```bash
# Build the container
docker build -t sound-player-container .

# Run with basic configuration
docker run -d \
  --name awos-sound-player \
  --device /dev/snd \
  -e VOLUME=80 \
  -e API_BASE_URL=https://your-weather-api.com \
  sound-player-container

# Run with full configuration
docker run -d \
  --name awos-sound-player \
  --device /dev/snd \
  --privileged \
  -e VOLUME=80 \
  -e ENABLE_RECORDING=true \
  -e ENABLE_HLS_STREAMING=true \
  -e ENABLE_S3_UPLOAD=true \
  -e S3_ACCESS_KEY_ID=your_key \
  -e S3_SECRET_ACCESS_KEY=your_secret \
  -e S3_ENDPOINT_URL=https://your-s3-endpoint.com \
  -e HLS_VPS_UPLOAD_URL=https://your-vps.com \
  sound-player-container
```

## Features

### AWOS Recording Implementation
- Records actual AWOS transmission audio instead of microphone input
- Temporarily suspends click detection during AWOS playback
- Coordinates relay control with audio recording
- Reuses existing recording infrastructure efficiently

### HLS Streaming
- Real-time audio streaming via HLS protocol
- Automatic segment creation and playlist management
- WebSocket-based upload to VPS for web access
- Configurable bitrate and segment duration

### Signal Detection
- Unified signal detector for both recording and click detection
- Configurable thresholds for different audio environments
- Automatic gain control and noise filtering
- Thread-safe operation with proper resource management

### GPIO Control
- Raspberry Pi GPIO integration for relay control
- Safe relay engagement/disengagement during transmission
- Automatic cleanup on system shutdown
- Mock implementation for testing without hardware

## Dependencies

### System Requirements
- Docker with audio device access
- ALSA audio system
- FFmpeg (for HLS encoding)
- GPIO access (for Raspberry Pi deployment)

### Python Packages
- soundfile: Audio file I/O
- scipy: Signal processing
- websockets: WebSocket client for VPS uploads
- boto3: S3 cloud storage
- ffmpeg-python: FFmpeg integration
- m3u8: HLS playlist management

## Troubleshooting

### Common Issues

1. **No audio input detected**
   - Check microphone permissions and device access
   - Verify ALSA configuration
   - Ensure container has `--device /dev/snd` access

2. **Click detection not working**
   - Adjust `SIGNAL_THRESHOLD_HIGH` and `SIGNAL_THRESHOLD_LOW`
   - Check `CLICK_MIN_DURATION` and `CLICK_MAX_DURATION` settings
   - Verify microphone sensitivity

3. **Recording failures**
   - Check storage permissions and available disk space
   - Verify S3 credentials and endpoint configuration
   - Review recording path settings

4. **HLS streaming issues**
   - Ensure FFmpeg is installed and accessible
   - Check HLS output directory permissions
   - Verify VPS upload URL and authentication

### Debug Mode

Enable detailed logging by setting log levels:
```bash
docker run -e PYTHONPATH=/app -e LOG_LEVEL=DEBUG sound-player-container
```

## File Structure

```
sound-player-container/
├── main.py                     # Main application entry point
├── audio_config.py             # Centralized configuration
├── audio_event_manager.py      # Core audio processing
├── unified_signal_detector.py  # Signal detection logic
├── awos_controller.py          # Click pattern handling
├── recording_controller.py     # Audio recording management
├── hls_audio_subscriber.py     # HLS streaming
├── hls_vps_uploader.py         # VPS upload via WebSocket
├── s3_upload_manager.py        # S3 cloud storage
├── gpio_controller.py          # GPIO relay control
├── awos_api_client.py          # Weather API client
├── audio_device_manager.py     # Audio device detection
├── requirements.txt            # Python dependencies
├── Dockerfile                  # Container definition
└── README.md                   # This documentation
```

## Security Notes

- Use secure S3 credentials and rotate regularly
- Implement proper VPS authentication for HLS uploads
- Ensure GPIO access is properly restricted
- Use HTTPS for all external API communications
- Regularly update container base image and dependencies

## Single Source of Truth Architecture

This container follows a single source of truth architecture where:
- Weather audio is fetched from the weather-api-container endpoint
- No local AWOS message generation logic is duplicated
- Audio event management, signal detection, S3 recording, click detection, WebSocket HLS upload, GPIO control, and sound injection functionality are preserved
- The system maintains clean separation of concerns between weather data generation and audio processing
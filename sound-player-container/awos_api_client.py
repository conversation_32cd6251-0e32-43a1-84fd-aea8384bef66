#!/usr/bin/env python3
"""
AWOS API Client

Simple HTTP client to fetch AWOS audio from weather-api-container endpoint.
"""

import os
import logging
import requests
from typing import Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class AWOSAPIClient:
    """Client for fetching AWOS audio from weather-api-container."""
    
    def __init__(self, api_base_url: Optional[str] = None):
        """
        Initialize AWOS API client.
        
        Args:
            api_base_url: Base URL for weather API (e.g., "http://weather-api:8000")
        """
        self.api_base_url = api_base_url or os.getenv("API_BASE_URL", "https://awosnew.skytraces.com")
        self.awos_endpoint = f"{self.api_base_url}/4FL5/awos.mp3"
        self.timeout = int(os.getenv("API_TIMEOUT_SECONDS", "10"))
        
        logger.info(f"AWOS API Client initialized with endpoint: {self.awos_endpoint}")
    
    def fetch_awos_audio(self, output_path: str) -> bool:
        """
        Fetch AWOS audio from API and save to file.
        
        Args:
            output_path: Path where to save the downloaded audio file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Fetching AWOS audio from {self.awos_endpoint}")
            
            # Make HTTP request to fetch AWOS audio
            response = requests.get(
                self.awos_endpoint,
                timeout=self.timeout,
                headers={
                    "Cache-Control": "no-cache",
                    "Pragma": "no-cache"
                }
            )
            
            # Check if request was successful
            response.raise_for_status()
            
            # Check content type
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('audio/'):
                logger.warning(f"Unexpected content type: {content_type}")
            
            # Save audio data to file
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            # Verify file was created and has content
            if output_file.exists() and output_file.stat().st_size > 0:
                logger.info(f"✓ AWOS audio saved to {output_path} ({output_file.stat().st_size} bytes)")
                return True
            else:
                logger.error(f"Failed to save AWOS audio to {output_path}")
                return False
                
        except requests.exceptions.Timeout:
            logger.error(f"Timeout fetching AWOS audio from {self.awos_endpoint}")
            return False
        except requests.exceptions.ConnectionError:
            logger.error(f"Connection error fetching AWOS audio from {self.awos_endpoint}")
            return False
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error fetching AWOS audio: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error fetching AWOS audio: {e}")
            return False
    
    def check_api_health(self) -> bool:
        """
        Check if the weather API is available.
        
        Returns:
            bool: True if API is healthy, False otherwise
        """
        try:
            health_endpoint = f"{self.api_base_url}/health"
            response = requests.get(health_endpoint, timeout=5)
            response.raise_for_status()
            return True
        except Exception as e:
            logger.debug(f"API health check failed: {e}")
            return False


def main():
    """Test the AWOS API client."""
    import tempfile
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Test client
    client = AWOSAPIClient()
    
    # Check API health
    if client.check_api_health():
        print("✓ API is healthy")
    else:
        print("✗ API health check failed")
    
    # Test fetching AWOS audio
    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as tmp_file:
        if client.fetch_awos_audio(tmp_file.name):
            print(f"✓ AWOS audio fetched successfully to {tmp_file.name}")
        else:
            print("✗ Failed to fetch AWOS audio")


if __name__ == "__main__":
    main()

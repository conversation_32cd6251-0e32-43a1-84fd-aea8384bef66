# Use Python slim base image for Debian Bookworm (matches RPi OS)
FROM python:3.11-slim-bookworm

# Install audio and GPIO packages
RUN apt-get update && apt-get install -y \
    alsa-utils \
    sox \
    ffmpeg \
    libportaudio2 \
    libportaudiocpp0 \
    portaudio19-dev \
    libgpiod2 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create a directory for the sound files
WORKDIR /app

# Copy requirements and install Python packages
COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy essential Python scripts
COPY main.py /app/main.py
COPY awos_api_client.py /app/awos_api_client.py
COPY audio_device_manager.py /app/audio_device_manager.py
COPY audio_event_manager.py /app/audio_event_manager.py
COPY unified_signal_detector.py /app/unified_signal_detector.py
COPY recording_controller.py /app/recording_controller.py
COPY awos_controller.py /app/awos_controller.py
COPY audio_config.py /app/audio_config.py
COPY s3_upload_manager.py /app/s3_upload_manager.py
COPY gpio_controller.py /app/gpio_controller.py
COPY hls_audio_subscriber.py /app/hls_audio_subscriber.py
COPY hls_vps_uploader.py /app/hls_vps_uploader.py

# Create directories for runtime data with proper permissions
RUN mkdir -p /app/recordings /app/hls && \
    chmod -R 777 /app/recordings && \
    chmod -R 777 /app/hls

# Create volumes for recordings and HLS output (internal to container)
VOLUME ["/app/recordings", "/app/hls"]

# Run as root to allow GPIO access
# Run the Python application directly
CMD ["python", "/app/main.py"]
#!/usr/bin/env python3
"""
Simple HTTP Server for HLS Streaming

This server serves HLS files from the /app/hls directory for local testing.
"""

import os
import logging
import threading
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse

from audio_config import AudioConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HLSRequestHandler(SimpleHTTPRequestHandler):
    """Custom HTTP request handler for HLS files."""
    
    def __init__(self, *args, **kwargs):
        # Set the directory to serve HLS files from
        self.hls_directory = Path(AudioConfig.HLS_OUTPUT_PATH)
        super().__init__(*args, directory=str(self.hls_directory), **kwargs)
    
    def end_headers(self):
        """Add CORS headers for cross-origin requests."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Add HLS-specific headers
        if self.path.endswith('.m3u8'):
            self.send_header('Content-Type', 'application/vnd.apple.mpegurl')
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        elif self.path.endswith('.ts'):
            self.send_header('Content-Type', 'video/mp2t')
            self.send_header('Cache-Control', 'max-age=3600')
        
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests."""
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # Serve HLS status page
        if path == '/status':
            self.serve_status_page()
        # Serve HLS files
        else:
            super().do_GET()
    

    
    def serve_status_page(self):
        """Serve HLS status information."""
        try:
            # Get HLS directory info
            hls_path = Path(AudioConfig.HLS_OUTPUT_PATH)
            
            status_info = {
                'hls_enabled': AudioConfig.ENABLE_HLS_STREAMING,
                'hls_directory': str(hls_path),
                'directory_exists': hls_path.exists(),
                'playlist_exists': (hls_path / 'playlist.m3u8').exists(),
                'segment_files': len(list(hls_path.glob('*.ts'))) if hls_path.exists() else 0,
                'total_files': len(list(hls_path.iterdir())) if hls_path.exists() else 0
            }
            
            # Try to get HLS subscriber stats from the running instance
            try:
                # Import the main application to get the running subscriber
                import main
                if hasattr(main, '_current_player') and main._current_player and main._current_player.hls_subscriber:
                    hls_stats = main._current_player.hls_subscriber.get_stats()
                    status_info['hls_stats'] = hls_stats
                else:
                    status_info['hls_stats'] = 'No active HLS subscriber found'
            except Exception as e:
                status_info['hls_stats_error'] = str(e)
            
            status_text = f"""HLS Streaming Status:
HLS Enabled: {status_info['hls_enabled']}
HLS Directory: {status_info['hls_directory']}
Directory Exists: {status_info['directory_exists']}
Playlist Exists: {status_info['playlist_exists']}
Segment Files: {status_info['segment_files']}
Total Files: {status_info['total_files']}

HLS Stats: {status_info.get('hls_stats', 'Not available')}
"""
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(status_text.encode('utf-8'))
            
        except Exception as e:
            error_text = f"Error getting status: {e}"
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(error_text.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to use our logger."""
        logger.info(f"{self.client_address[0]} - {format % args}")


class HLSServer:
    """HLS HTTP Server for serving streaming files."""
    
    def __init__(self, host='0.0.0.0', port=8080):
        """Initialize the HLS server."""
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.running = False
    
    def start(self):
        """Start the HLS server."""
        if self.running:
            logger.warning("HLS server is already running")
            return
        
        try:
            # Create HLS directory if it doesn't exist
            hls_path = Path(AudioConfig.HLS_OUTPUT_PATH)
            hls_path.mkdir(parents=True, exist_ok=True)
            
            # Create HTTP server
            self.server = HTTPServer((self.host, self.port), HLSRequestHandler)
            
            # Start server in background thread
            self.server_thread = threading.Thread(
                target=self.server.serve_forever,
                daemon=True
            )
            self.server_thread.start()
            self.running = True
            
            logger.info(f"HLS server started on http://{self.host}:{self.port}")
            logger.info(f"Playlist URL: http://{self.host}:{self.port}/playlist.m3u8")
            logger.info(f"Status page: http://{self.host}:{self.port}/status")
            
        except Exception as e:
            logger.error(f"Failed to start HLS server: {e}")
            self.running = False
    
    def stop(self):
        """Stop the HLS server."""
        if not self.running:
            return
        
        try:
            if self.server:
                self.server.shutdown()
                self.server.server_close()
            
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=5.0)
            
            self.running = False
            logger.info("HLS server stopped")
            
        except Exception as e:
            logger.error(f"Error stopping HLS server: {e}")
    
    def is_running(self):
        """Check if the server is running."""
        return self.running


def main():
    """Main entry point for standalone server."""
    logger.info("Starting HLS Server...")
    
    # Check if HLS is enabled
    if not AudioConfig.ENABLE_HLS_STREAMING:
        logger.warning("HLS streaming is not enabled in configuration")
        logger.info("Set ENABLE_HLS_STREAMING=true to enable HLS streaming")
    
    server = HLSServer()
    
    try:
        server.start()
        
        if server.is_running():
            logger.info("HLS server is running. Press Ctrl+C to stop.")
            
            # Keep the main thread alive
            while server.is_running():
                import time
                time.sleep(1)
        else:
            logger.error("Failed to start HLS server")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Server error: {e}")
        return 1
    finally:
        server.stop()
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
